"""
Azure AI Document Translation Service - PDF翻译工具
使用Azure Document Translation API翻译本地PDF文件
"""

import requests
import os
import time
import json
from pathlib import Path
from typing import Optional, Dict, Any


class AzurePDFTranslator:
    def __init__(self, endpoint: str, subscription_key: str):
        """
        初始化Azure PDF翻译器
        
        Args:
            endpoint (str): Azure Document Translation服务端点
            subscription_key (str): Azure订阅密钥
        """
        self.endpoint = endpoint.rstrip('/')
        self.subscription_key = subscription_key
        self.api_version = "2023-11-01-preview"
        
        # 设置请求头
        self.headers = {
            "Ocp-Apim-Subscription-Key": self.subscription_key,
            "Content-Type": "application/json"
        }
        
        # 支持的文件类型
        self.supported_content_types = {
            '.pdf': 'application/pdf',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            '.html': 'text/html',
            '.txt': 'text/plain'
        }
    
    def get_supported_languages(self) -> Dict[str, Any]:
        """获取支持的语言列表"""
        try:
            url = f"{self.endpoint}/translator/document/languages"
            params = {"api-version": self.api_version}
            
            response = requests.get(url, headers=self.headers, params=params)
            response.raise_for_status()
            
            return response.json()
        except Exception as e:
            print(f"获取支持语言列表失败: {e}")
            return {}
    
    def validate_file(self, file_path: str) -> bool:
        """验证文件是否存在且格式支持"""
        if not os.path.exists(file_path):
            print(f"错误: 文件不存在 - {file_path}")
            return False
        
        file_ext = Path(file_path).suffix.lower()
        if file_ext not in self.supported_content_types:
            print(f"错误: 不支持的文件格式 - {file_ext}")
            print(f"支持的格式: {list(self.supported_content_types.keys())}")
            return False
        
        # 检查文件大小 (Azure限制为40MB)
        file_size = os.path.getsize(file_path)
        max_size = 40 * 1024 * 1024  # 40MB
        if file_size > max_size:
            print(f"错误: 文件大小超过限制 ({file_size / 1024 / 1024:.2f}MB > 40MB)")
            return False
        
        return True
    
    def translate_document_sync(self, 
                               input_file: str, 
                               output_file: str,
                               source_language: str = "auto",
                               target_language: str = "zh-Hans") -> bool:
        """
        同步翻译文档
        
        Args:
            input_file (str): 输入文件路径
            output_file (str): 输出文件路径
            source_language (str): 源语言代码 (默认auto自动检测)
            target_language (str): 目标语言代码 (默认zh-Hans简体中文)
            
        Returns:
            bool: 翻译是否成功
        """
        try:
            # 验证输入文件
            if not self.validate_file(input_file):
                return False
            
            # 构建URL
            url = f"{self.endpoint}/translator/document:translate"
            
            # 设置参数
            params = {
                "sourceLanguage": source_language,
                "targetLanguage": target_language,
                "api-version": self.api_version
            }
            
            # 获取文件内容类型
            file_ext = Path(input_file).suffix.lower()
            content_type = self.supported_content_types[file_ext]
            
            print(f"开始翻译文档: {input_file}")
            print(f"源语言: {source_language}, 目标语言: {target_language}")
            
            # 打开文件并发送请求
            with open(input_file, "rb") as document:
                files = {
                    "document": (os.path.basename(input_file), document, content_type)
                }
                
                # 发送POST请求
                response = requests.post(
                    url, 
                    headers={"Ocp-Apim-Subscription-Key": self.subscription_key},
                    files=files, 
                    params=params,
                    timeout=300  # 5分钟超时
                )
                
                response.raise_for_status()
            
            # 保存翻译结果
            os.makedirs(os.path.dirname(output_file), exist_ok=True)
            with open(output_file, "wb") as output_document:
                output_document.write(response.content)
            
            print(f"翻译完成! 输出文件: {output_file}")
            print(f"文件大小: {len(response.content) / 1024:.2f} KB")
            
            return True
            
        except requests.exceptions.RequestException as e:
            print(f"请求错误: {e}")
            if hasattr(e, 'response') and e.response is not None:
                print(f"响应状态码: {e.response.status_code}")
                print(f"响应内容: {e.response.text}")
            return False
        except Exception as e:
            print(f"翻译过程中出错: {e}")
            return False
    
    def batch_translate(self, 
                       input_folder: str, 
                       output_folder: str,
                       source_language: str = "auto",
                       target_language: str = "zh-Hans") -> Dict[str, bool]:
        """
        批量翻译文件夹中的文档
        
        Args:
            input_folder (str): 输入文件夹路径
            output_folder (str): 输出文件夹路径
            source_language (str): 源语言代码
            target_language (str): 目标语言代码
            
        Returns:
            Dict[str, bool]: 每个文件的翻译结果
        """
        results = {}
        
        if not os.path.exists(input_folder):
            print(f"错误: 输入文件夹不存在 - {input_folder}")
            return results
        
        # 创建输出文件夹
        os.makedirs(output_folder, exist_ok=True)
        
        # 获取所有支持的文件
        supported_files = []
        for ext in self.supported_content_types.keys():
            supported_files.extend(Path(input_folder).glob(f"*{ext}"))
        
        print(f"找到 {len(supported_files)} 个支持的文件")
        
        for i, file_path in enumerate(supported_files, 1):
            print(f"\n处理文件 {i}/{len(supported_files)}: {file_path.name}")
            
            # 构建输出文件路径
            output_file = os.path.join(output_folder, f"translated_{file_path.name}")
            
            # 翻译文件
            success = self.translate_document_sync(
                str(file_path), 
                output_file, 
                source_language, 
                target_language
            )
            
            results[str(file_path)] = success
            
            # 添加延迟避免API限制
            if i < len(supported_files):
                time.sleep(1)
        
        return results
    
    def print_language_codes(self):
        """打印常用语言代码"""
        common_languages = {
            "auto": "自动检测",
            "en": "英语",
            "zh-Hans": "简体中文",
            "zh-Hant": "繁体中文",
            "ja": "日语",
            "ko": "韩语",
            "fr": "法语",
            "de": "德语",
            "es": "西班牙语",
            "it": "意大利语",
            "pt": "葡萄牙语",
            "ru": "俄语",
            "ar": "阿拉伯语"
        }
        
        print("常用语言代码:")
        print("-" * 30)
        for code, name in common_languages.items():
            print(f"{code:10} - {name}")


def main():
    """主函数 - 示例用法"""
    
    # 配置Azure服务信息
    ENDPOINT = "https://mzhou-m9k3o49z-swedencentral.cognitiveservices.azure.com/"  # 替换为您的端点
    SUBSCRIPTION_KEY = "57TsCKHuzWuHeLgH8FpA4ACeG72Fv7xB35jodfHUiXSzfm0UZeY7JQQJ99BDACfhMk5XJ3w3AAAAACOGoK22"  # 替换为您的订阅密钥
    
    # 检查配置
    if "your-resource-name" in ENDPOINT or "your-subscription-key" in SUBSCRIPTION_KEY:
        print("请先配置Azure服务端点和订阅密钥!")
        print("在main()函数中修改ENDPOINT和SUBSCRIPTION_KEY变量")
        return
    
    # 创建翻译器实例
    translator = AzurePDFTranslator(ENDPOINT, SUBSCRIPTION_KEY)
    
    # 打印语言代码参考
    translator.print_language_codes()
    
    # 示例1: 翻译单个PDF文件
    input_pdf = "C:\\Users\\<USER>\\Downloads\\splitted_pdfs\\split_part_1.pdf"  # 替换为您的PDF文件路径
    output_pdf = "C:\\Users\\<USER>\\Downloads\\splitted_pdfs\\translated_example.pdf"
    
    if os.path.exists(input_pdf):
        print(f"\n开始翻译单个文件: {input_pdf}")
        success = translator.translate_document_sync(
            input_pdf, 
            output_pdf,
            source_language="en",  # 英语
            target_language="zh-Hans"  # 简体中文
        )
        
        if success:
            print("单文件翻译成功!")
        else:
            print("单文件翻译失败!")
    else:
        print(f"示例文件不存在: {input_pdf}")
    
    # # 示例2: 批量翻译文件夹
    # input_folder = "documents"  # 替换为您的文档文件夹
    # output_folder = "translated_documents"
    
    # if os.path.exists(input_folder):
    #     print(f"\n开始批量翻译文件夹: {input_folder}")
    #     results = translator.batch_translate(
    #         input_folder,
    #         output_folder,
    #         source_language="auto",  # 自动检测
    #         target_language="zh-Hans"  # 简体中文
    #     )
        
    #     # 打印结果统计
    #     success_count = sum(results.values())
    #     total_count = len(results)
    #     print(f"\n批量翻译完成: {success_count}/{total_count} 个文件成功")
        
    #     for file_path, success in results.items():
    #         status = "✓" if success else "✗"
    #         print(f"{status} {os.path.basename(file_path)}")
    # else:
    #     print(f"示例文件夹不存在: {input_folder}")


if __name__ == "__main__":
    main()
