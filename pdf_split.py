import os
from PyPDF2 import PdfReader, PdfWriter

def split_pdf(input_pdf_path, output_dir, max_pages=100):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    reader = PdfReader(input_pdf_path)
    total_pages = len(reader.pages)
    part = 1

    for start in range(0, total_pages, max_pages):
        writer = PdfWriter()
        end = min(start + max_pages, total_pages)
        for i in range(start, end):
            writer.add_page(reader.pages[i])
        output_path = os.path.join(output_dir, f"split_part_{part}.pdf")
        with open(output_path, "wb") as out_file:
            writer.write(out_file)
        print(f"Created: {output_path} (pages {start+1}-{end})")
        part += 1

if __name__ == "__main__":
    input_pdf = "C:\\Users\\<USER>\\Downloads\\附件1_万华化学集团股份有限公司年产20000吨PDMS扩能项目环境影响报告书报批稿.pdf"  # Replace with your PDF file path
    output_folder = "C:\\Users\\<USER>\\Downloads\\splitted_pdfs"
    split_pdf(input_pdf, output_folder, max_pages=100)