# This file was auto-generated by Fern from our API Definition.

import enum
import typing

T_Result = typing.TypeVar("T_Result")


class ObservationType(str, enum.Enum):
    SPAN = "SPAN"
    GENERATION = "GENERATION"
    EVENT = "EVENT"

    def visit(
        self,
        span: typing.Callable[[], T_Result],
        generation: typing.Callable[[], T_Result],
        event: typing.Callable[[], T_Result],
    ) -> T_Result:
        if self is ObservationType.SPAN:
            return span()
        if self is ObservationType.GENERATION:
            return generation()
        if self is ObservationType.EVENT:
            return event()
