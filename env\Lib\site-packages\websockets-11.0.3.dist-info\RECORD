websockets-11.0.3.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
websockets-11.0.3.dist-info/LICENSE,sha256=D0RRSZisognTSC0QIEqK3yqkKW_xV6NqXAki8igGMtM,1538
websockets-11.0.3.dist-info/METADATA,sha256=edSUgF5gqtTk5NUj2OGoTcYHQ6akKjvh1l4Y5ckZ7Xw,6795
websockets-11.0.3.dist-info/RECORD,,
websockets-11.0.3.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets-11.0.3.dist-info/WHEEL,sha256=9wvhO-5NhjjD8YmmxAvXTPQXMDOZ50W5vklzeoqFtkM,102
websockets-11.0.3.dist-info/top_level.txt,sha256=CMpdKklxKsvZgCgyltxUWOHibZXZ1uYIVpca9xsQ8Hk,11
websockets/__init__.py,sha256=3xrKLebGbcbf1cx0bgD5NRXsdE0u1DfysCpNvgBKaGM,3540
websockets/__main__.py,sha256=BMtbQ-dwHfmXmz2STx4nAcdliEKuj2wQXyxe23Vp1S0,4903
websockets/__pycache__/__init__.cpython-311.pyc,,
websockets/__pycache__/__main__.cpython-311.pyc,,
websockets/__pycache__/auth.cpython-311.pyc,,
websockets/__pycache__/client.cpython-311.pyc,,
websockets/__pycache__/connection.cpython-311.pyc,,
websockets/__pycache__/datastructures.cpython-311.pyc,,
websockets/__pycache__/exceptions.cpython-311.pyc,,
websockets/__pycache__/frames.cpython-311.pyc,,
websockets/__pycache__/headers.cpython-311.pyc,,
websockets/__pycache__/http.cpython-311.pyc,,
websockets/__pycache__/http11.cpython-311.pyc,,
websockets/__pycache__/imports.cpython-311.pyc,,
websockets/__pycache__/protocol.cpython-311.pyc,,
websockets/__pycache__/server.cpython-311.pyc,,
websockets/__pycache__/streams.cpython-311.pyc,,
websockets/__pycache__/typing.cpython-311.pyc,,
websockets/__pycache__/uri.cpython-311.pyc,,
websockets/__pycache__/utils.cpython-311.pyc,,
websockets/__pycache__/version.cpython-311.pyc,,
websockets/auth.py,sha256=8OBgaeuLFDKFBD537TcgFX5tnDL5gUb3LzVD5uIXvVE,143
websockets/client.py,sha256=GrI-pWcY5FFGzXtx2TzkvOGMh5iSWaBFpnxTl4zoeXg,12776
websockets/connection.py,sha256=dnjnwNSlmNt86T_QKEU6kXhfGA_nHUPgoSn2_iS67tc,346
websockets/datastructures.py,sha256=b-J8Hq-Ori5zXkrdFpV6iXh2kGFrf8m4PHKp_49zmXc,5938
websockets/exceptions.py,sha256=lWyA8YMjraX1Vy_g9yeWhO7HIN3XbhEbjGRhyK3mJ34,10546
websockets/extensions/__init__.py,sha256=HdQaQhOVkCR5RJVRKXG5Xmb6cMpAhLaKgRikYRzO64E,102
websockets/extensions/__pycache__/__init__.cpython-311.pyc,,
websockets/extensions/__pycache__/base.cpython-311.pyc,,
websockets/extensions/__pycache__/permessage_deflate.cpython-311.pyc,,
websockets/extensions/base.py,sha256=Udkz03dw1Jb85u0JAD7tWflarEX_yLqumgjY9DFXvaw,3404
websockets/extensions/permessage_deflate.py,sha256=BBQSAfHHtVBoOlfyT9kc-sD6AnBay1cr5xeMquK2fuo,25442
websockets/frames.py,sha256=LVCOKPpS3EV7Cs6EL0i8sRq9ZoZ1zjUAQbzbmBwaE1Q,12986
websockets/headers.py,sha256=ZSdzxVAcJ_O4fEg-PK3v7uuMWoOOR936TOdmt2ceg4U,16707
websockets/http.py,sha256=yGB2GWFxQk52lRmzLWC5ks2U6Eej39n6rIYF-nff7jw,674
websockets/http11.py,sha256=GDvgVqCxLsX5RXF6v-2OZvsluFUy0J0PsqT_kFuNPXw,12929
websockets/imports.py,sha256=9BnKfdMY-4RW_uRv_DkOhDs1i6ZhrWIp245QWZagajc,2889
websockets/legacy/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/legacy/__pycache__/__init__.cpython-311.pyc,,
websockets/legacy/__pycache__/async_timeout.cpython-311.pyc,,
websockets/legacy/__pycache__/auth.cpython-311.pyc,,
websockets/legacy/__pycache__/client.cpython-311.pyc,,
websockets/legacy/__pycache__/compatibility.cpython-311.pyc,,
websockets/legacy/__pycache__/framing.cpython-311.pyc,,
websockets/legacy/__pycache__/handshake.cpython-311.pyc,,
websockets/legacy/__pycache__/http.cpython-311.pyc,,
websockets/legacy/__pycache__/protocol.cpython-311.pyc,,
websockets/legacy/__pycache__/server.cpython-311.pyc,,
websockets/legacy/async_timeout.py,sha256=udgTU_qsiWiwPp8b-NSyqvKtksunihXlMxOX91ISKkE,8805
websockets/legacy/auth.py,sha256=z7LHgWN2rd2ugDNCt2Ie17ocHT-t8a8bWmm72fFqUow,6471
websockets/legacy/client.py,sha256=sNx7S3z6Cg_FXwGXS54Bc4zn26OlbLp7kfjIX9UfCes,27268
websockets/legacy/compatibility.py,sha256=l85rEQsn-fchqCGo0eaAlXtRp-Bz7niGTgGLsu9cniU,791
websockets/legacy/framing.py,sha256=4AljNO-aVi0pDIt-7mKp_srw5w-HfrTarBgnJHo5a5I,5174
websockets/legacy/handshake.py,sha256=Lk_xqIE1kaU9MHip-hmAXcaWc-pCYynG_JtSukQlImI,5644
websockets/legacy/http.py,sha256=ZdG1e9R0MjO-_9FAyRCPZLJMH4Zqb5U4dSO9RL4QG2A,7139
websockets/legacy/protocol.py,sha256=pRbWoXLSOLCVvJf5QwAjxsohOqorbB3rIBeKj_gnACU,64981
websockets/legacy/server.py,sha256=MAdkHMk6VDmxrDrfcyFQHUIw58s5jdwiLulFq7_TQvA,46428
websockets/protocol.py,sha256=6v5KbqQpc8xGteFIUE4EK-6mv2vJVAitf61_BflmKy0,24529
websockets/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/server.py,sha256=EWQ8MzOV8zcM5ZR_a0Hd4cO94kYBzLrDOC_00rcR-n8,21432
websockets/speedups.c,sha256=Ihn5ANJ_X-CkK83BSige-5yXWP5_2Ob90BAzx_7vJpo,6057
websockets/speedups.cp311-win_amd64.pyd,sha256=5aXlTtlNi1-vQZLIbAAR_-uLSOCmZ141Q3rg119cPrc,11776
websockets/streams.py,sha256=PbVlfaUjsRo70fO6zOnvae-9M3Bnh2PUu43f27WHknc,4189
websockets/sync/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
websockets/sync/__pycache__/__init__.cpython-311.pyc,,
websockets/sync/__pycache__/client.cpython-311.pyc,,
websockets/sync/__pycache__/compatibility.cpython-311.pyc,,
websockets/sync/__pycache__/connection.cpython-311.pyc,,
websockets/sync/__pycache__/messages.cpython-311.pyc,,
websockets/sync/__pycache__/server.cpython-311.pyc,,
websockets/sync/__pycache__/utils.cpython-311.pyc,,
websockets/sync/client.py,sha256=WH3thAuQFsZZqBxhe1he8ds7C9sMqvghTwk34BNzT5Q,11593
websockets/sync/compatibility.py,sha256=YAwxJLlA2i2DqV1rmIAAGT6cmOio1FfsLEbF3w520w0,576
websockets/sync/connection.py,sha256=jstGhqTCCFHLsqj9tV4W2v2P7orz5K6Ag2WtE0ictoc,30048
websockets/sync/messages.py,sha256=j7QdUVmQadUgygy9bwgdwYodJyyOAjFnyKfOLvV_4tc,9765
websockets/sync/server.py,sha256=PW0KA-E4a8LSkQWGZFNPocDTdOoe6xFjCdpdJl31MZM,19190
websockets/sync/utils.py,sha256=Pth5iuZHcoNphKnda238_YbES1W9Mnam8KTJYRhPphs,1197
websockets/typing.py,sha256=YNw2qGqZgZTMB4kqfxincSX0xfZMT_QglvZFUXwDucs,1444
websockets/uri.py,sha256=dppB507un7zZrxwvrVLScSFWcqqzdwV05WOxnagsYaA,3323
websockets/utils.py,sha256=nz3tb3CXB6xtssMM2fUFDXPqHPq1XeNRFog9tAzJpPk,1201
websockets/version.py,sha256=eT4uDoaOBjtzlK9gGA-VM6HlR8sjFtDo5SC__ryXwlg,2801
