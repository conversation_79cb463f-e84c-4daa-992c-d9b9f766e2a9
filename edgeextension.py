"""
Edge浏览器扩展安装和自动化脚本
使用Selenium WebDriver控制Edge浏览器，安装扩展并打开Google
"""

import os
import time
from selenium import webdriver
from selenium.webdriver.edge.service import Service
from selenium.webdriver.edge.options import Options
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, WebDriverException


class EdgeExtensionManager:
    def __init__(self, extension_path=None, headless=False):
        """
        初始化Edge浏览器管理器
        
        Args:
            extension_path (str): 扩展文件路径（.crx文件或解压后的文件夹）
            headless (bool): 是否以无头模式运行
        """
        self.extension_path = extension_path
        self.headless = headless
        self.driver = None
        
    def setup_edge_options(self):
        """配置Edge浏览器选项"""
        options = Options()
        
        # 基本配置
        if self.headless:
            options.add_argument("--headless")
        
        # 禁用一些安全限制以便安装扩展
        options.add_argument("--disable-web-security")
        options.add_argument("--disable-features=VizDisplayCompositor")
        options.add_argument("--disable-extensions-file-access-check")
        options.add_argument("--disable-extensions-http-throttling")
        options.add_argument("--disable-gpu")
        options.add_argument("--no-sandbox")
        
        # 如果有扩展路径，添加扩展
        if self.extension_path:
            if os.path.isdir(self.extension_path):
                # 如果是文件夹（解压后的扩展）
                options.add_argument(f"--load-extension={self.extension_path}")
                print(f"准备加载解压扩展: {self.extension_path}")
            elif self.extension_path.endswith('.crx'):
                # 如果是.crx文件
                options.add_extension(self.extension_path)
                print(f"准备加载CRX扩展: {self.extension_path}")
            else:
                print(f"警告: 不支持的扩展格式: {self.extension_path}")
        
        # 设置用户数据目录（可选，用于保持扩展状态）
        # options.add_argument("--user-data-dir=./edge_profile")
        
        return options
    
    def start_browser(self):
        """启动Edge浏览器"""
        try:
            # 配置Edge选项
            options = self.setup_edge_options()
            
            # 创建Edge WebDriver
            # 注意：需要确保已安装Edge WebDriver
            self.driver = webdriver.Edge(options=options)
            
            print("Edge浏览器启动成功")
            
            # 等待浏览器完全加载
            time.sleep(3)
            
            return True
            
        except WebDriverException as e:
            print(f"启动Edge浏览器失败: {e}")
            print("请确保已安装Edge WebDriver")
            print("下载地址: https://developer.microsoft.com/en-us/microsoft-edge/tools/webdriver/")
            return False
        except Exception as e:
            print(f"未知错误: {e}")
            return False
    
    def check_extension_installed(self):
        """检查扩展是否成功安装"""
        try:
            # 打开扩展管理页面
            self.driver.get("edge://extensions/")
            time.sleep(2)
            
            # 查找扩展元素
            extensions = self.driver.find_elements(By.CSS_SELECTOR, "extensions-item")
            
            if extensions:
                print(f"检测到 {len(extensions)} 个扩展")
                for i, ext in enumerate(extensions):
                    try:
                        name = ext.find_element(By.CSS_SELECTOR, "#name").text
                        print(f"扩展 {i+1}: {name}")
                    except:
                        print(f"扩展 {i+1}: 无法获取名称")
                return True
            else:
                print("未检测到任何扩展")
                return False
                
        except Exception as e:
            print(f"检查扩展时出错: {e}")
            return False
    
    def open_google(self):
        """打开Google网站"""
        try:
            print("正在打开Google...")
            self.driver.get("https://www.google.com")
            
            # 等待页面加载
            wait = WebDriverWait(self.driver, 10)
            wait.until(EC.presence_of_element_located((By.NAME, "q")))
            
            print("Google页面加载成功")
            print(f"当前页面标题: {self.driver.title}")
            
            return True
            
        except TimeoutException:
            print("Google页面加载超时")
            return False
        except Exception as e:
            print(f"打开Google时出错: {e}")
            return False
    
    def close_browser(self):
        """关闭浏览器"""
        if self.driver:
            self.driver.quit()
            print("浏览器已关闭")
    
    def run(self):
        """运行完整流程"""
        print("=== Edge浏览器扩展安装和自动化 ===")
        
        # 启动浏览器
        if not self.start_browser():
            return False
        
        try:
            # 检查扩展安装情况
            if self.extension_path:
                print("\n检查扩展安装状态...")
                self.check_extension_installed()
            
            # 打开Google
            print("\n打开Google网站...")
            if self.open_google():
                print("\n任务完成！浏览器将保持打开状态...")
                print("按Enter键关闭浏览器...")
                input()
            
            return True
            
        except KeyboardInterrupt:
            print("\n用户中断操作")
            return False
        except Exception as e:
            print(f"\n运行过程中出错: {e}")
            return False
        finally:
            self.close_browser()


def main():
    """主函数"""
    # 配置参数
    extension_path = None  # 设置为您的扩展路径，例如: "C:/path/to/extension" 或 "extension.crx"
    headless = False  # 设置为True以无头模式运行
    
    # 示例扩展路径（请根据实际情况修改）
    extension_path = "C:/Users/<USER>/Downloads/bypass-paywalls-chrome-clean-master"
    # extension_path = "C:/Users/<USER>/Downloads/extension.crx"
    
    print("Edge浏览器扩展管理器")
    print("=" * 40)
    
    if extension_path:
        if not os.path.exists(extension_path):
            print(f"错误: 扩展路径不存在: {extension_path}")
            return
        print(f"扩展路径: {extension_path}")
    else:
        print("未指定扩展路径，将只启动浏览器并打开Google")
    
    # 创建管理器实例
    manager = EdgeExtensionManager(extension_path=extension_path, headless=headless)
    
    # 运行
    success = manager.run()
    
    if success:
        print("程序执行完成")
    else:
        print("程序执行失败")


if __name__ == "__main__":
    main()
